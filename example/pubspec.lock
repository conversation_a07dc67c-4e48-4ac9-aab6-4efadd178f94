# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.11"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.5.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  charset_converter:
    dependency: transitive
    description:
      name: charset_converter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.15.0"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.1"
  esc_pos_bluetooth:
    dependency: transitive
    description:
      path: "."
      ref: master
      resolved-ref: c721fea984e78f8ccf5f48a924bf1a3c071c01c3
      url: "https://bitbucket.org/umomos/esc_pos_bluetooth.git"
    source: git
    version: "0.4.1"
  esc_pos_printer:
    dependency: transitive
    description:
      path: "."
      ref: master
      resolved-ref: "45490f3b51a2b37a043bdb5c4c66071cb2229f4a"
      url: "https://bitbucket.org/umomos/esc_pos_printer.git"
    source: git
    version: "4.1.0"
  esc_pos_utils:
    dependency: "direct main"
    description:
      path: "."
      ref: dev
      resolved-ref: "41de7f2001a206b7ac8943c92024947673f2afbc"
      url: "https://bitbucket.org/umomos/esc_pos_utils.git"
    source: git
    version: "1.1.0"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  fast_gbk:
    dependency: transitive
    description:
      name: fast_gbk
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_bluetooth_basic:
    dependency: transitive
    description:
      path: "."
      ref: master
      resolved-ref: dc45fbea67168e0db6433955b1b2cbf3c0599944
      url: "https://bitbucket.org/umomos/flutter_bluetooth_basic.git"
    source: git
    version: "0.1.7"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  get:
    dependency: "direct main"
    description:
      name: get
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.6.1"
  hex:
    dependency: transitive
    description:
      name: hex
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  image:
    dependency: "direct main"
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.1"
  intl:
    dependency: transitive
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.3"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.1"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.10"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  okshop_common:
    dependency: "direct overridden"
    description:
      path: "."
      ref: dev
      resolved-ref: "80bfb84d8162987de66b303b1cba4c145dc57f81"
      url: "https://bitbucket.org/umomos/okshop_common.git"
    source: git
    version: "0.0.1"
  okshop_esc_pos:
    dependency: "direct main"
    description:
      path: ".."
      relative: true
    source: path
    version: "0.0.1"
  okshop_model:
    dependency: "direct main"
    description:
      path: "."
      ref: dev
      resolved-ref: "94eea51b386b25102b7617918668d37d3eb6a964"
      url: "https://bitbucket.org/umomos/okshop_model.git"
    source: git
    version: "0.0.1"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.0"
  ping_discover_network:
    dependency: transitive
    description:
      name: ping_discover_network
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0+1"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.5.1"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.26.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.19"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  wifi:
    dependency: transitive
    description:
      name: wifi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.5"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.1.2"
sdks:
  dart: ">=2.12.0 <3.0.0"
  flutter: ">=1.20.0"
