name: okshop_esc_pos_example
description: Demonstrates how to use the okshop_esc_pos plugin.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

environment:
  sdk: ">=2.7.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  esc_pos_utils:
    # path: ../esc_pos_utils
    git:
        url: https://bitbucket.org/umomos/esc_pos_utils.git
        ref: master
  okshop_model:
    # path: ../../okshop_model
    git:
        url: https://bitbucket.org/umomos/okshop_model.git
        ref: master
  okshop_esc_pos:
    # When depending on this package from a real application you should use:
    #   okshop_esc_pos: ^x.y.z
    # See https://dart.dev/tools/pub/dependencies#version-constraints
    # The example app is bundled with the plugin so we use a path dependency on
    # the parent directory to use the current plugin's version.
    path: ../
  image: ^3.0.2
  get: ^4.6.1
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2

dependency_overrides:
  okshop_common:
    # path: ../okshop_common
    git:
        url: https://bitbucket.org/umomos/okshop_common.git
        ref: dev
  okshop_model:
    # path: ../okshop_model
    git:
        url: https://bitbucket.org/umomos/okshop_model.git
        ref: dev
  esc_pos_utils:
    # path: ../esc_pos_utils
    git:
        url: https://bitbucket.org/umomos/esc_pos_utils.git
        ref: dev

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/logo.png
    - assets/receipt.json
    - assets/invoice.json

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
