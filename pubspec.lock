# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.11"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.5.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  charset_converter:
    dependency: transitive
    description:
      name: charset_converter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.15.0"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.1"
  esc_pos_bluetooth:
    dependency: "direct main"
    description:
      path: "."
      ref: master
      resolved-ref: c721fea984e78f8ccf5f48a924bf1a3c071c01c3
      url: "https://bitbucket.org/umomos/esc_pos_bluetooth.git"
    source: git
    version: "0.4.1"
  esc_pos_printer:
    dependency: "direct main"
    description:
      path: "."
      ref: master
      resolved-ref: "67dd64718078a64c885313a849acd64bc98883a9"
      url: "https://bitbucket.org/umomos/esc_pos_printer.git"
    source: git
    version: "4.1.0"
  esc_pos_utils:
    dependency: "direct main"
    description:
      path: "."
      ref: master
      resolved-ref: "41de7f2001a206b7ac8943c92024947673f2afbc"
      url: "https://bitbucket.org/umomos/esc_pos_utils.git"
    source: git
    version: "1.1.0"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  fast_gbk:
    dependency: transitive
    description:
      name: fast_gbk
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  ffi:
    dependency: transitive
    description:
      name: ffi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.4"
  flat_buffers:
    dependency: transitive
    description:
      name: flat_buffers
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_bluetooth_basic:
    dependency: "direct main"
    description:
      path: "."
      ref: master
      resolved-ref: dc45fbea67168e0db6433955b1b2cbf3c0599944
      url: "https://bitbucket.org/umomos/flutter_bluetooth_basic.git"
    source: git
    version: "0.1.7"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  hex:
    dependency: transitive
    description:
      name: hex
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  image:
    dependency: "direct main"
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.1"
  intl:
    dependency: transitive
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.3"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.1"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.10"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  objectbox:
    dependency: transitive
    description:
      name: objectbox
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.0"
  objectbox_flutter_libs:
    dependency: transitive
    description:
      name: objectbox_flutter_libs
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.0"
  okshop_common:
    dependency: "direct main"
    description:
      path: "."
      ref: master
      resolved-ref: cdec14ce108bf93939c2d74fe80d4667742f4636
      url: "https://bitbucket.org/umomos/okshop_common.git"
    source: git
    version: "0.0.1"
  okshop_model:
    dependency: "direct main"
    description:
      path: "."
      ref: master
      resolved-ref: "587661a5ea71d55419f8767fcdafc543af694ea7"
      url: "https://bitbucket.org/umomos/okshop_model.git"
    source: git
    version: "0.0.1"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.6"
  path_provider_macos:
    dependency: transitive
    description:
      name: path_provider_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.6"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.6"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.0"
  ping_discover_network:
    dependency: "direct main"
    description:
      name: ping_discover_network
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0+1"
  platform:
    dependency: transitive
    description:
      name: platform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.5.1"
  process:
    dependency: transitive
    description:
      name: process
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.3"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.26.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  stream_transform:
    dependency: "direct main"
    description:
      name: stream_transform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.19"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  wifi:
    dependency: "direct main"
    description:
      name: wifi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.5"
  win32:
    dependency: transitive
    description:
      name: win32
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0+3"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.1.2"
sdks:
  dart: ">=2.12.0 <3.0.0"
  flutter: ">=2.0.0"
