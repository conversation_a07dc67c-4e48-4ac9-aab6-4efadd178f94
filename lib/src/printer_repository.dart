import 'dart:async';

import 'package:esc_pos_bluetooth/esc_pos_bluetooth.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:ping_discover_network/ping_discover_network.dart';
import 'package:wifi/wifi.dart';

import 'constants.dart';
import 'enums.dart';
import 'extension.dart';

class PrinterRepository {
  final _printerManager = PrinterBluetoothManager();
  final _timeout = 4;

  static Stream<NetworkAddress> _discover({
    int port = 9100,
    Duration timeout = const Duration(milliseconds: 400),
  }) async* {
    final ip = await Wifi.ip;
    final subnet = ip.substring(0, ip.lastIndexOf('.'));
    yield* NetworkAnalyzer.discover2(
      subnet,
      port,
      timeout: timeout ?? Duration(milliseconds: 400),
    );
  }

  static Future<Iterable<SettingLabel>> _lanScan(
      [num port = kDefaultPort]) async {
    final stream = _discover(port: port);
    final ls = await stream.toList();
    return ls
        .where((element) => element.exists == true)
        .map((e) => e.asSettingLabel(port));
  }

  Future<Iterable<SettingLabel>> scan([List<PrinterType> types]) async {
    types ??= [PrinterType.net, PrinterType.bth];
    Iterable<Future<Iterable<SettingLabel>>> children(
        Iterable<PrinterType> types) sync* {
      if (types.contains(PrinterType.net)) {
        yield _lanScan();
      }
      if (types.contains(PrinterType.bth)) {
        yield _bluetoothScen();
      }
    }

    final ls = await Future.wait(children(types));
    final mergedList =
        ls.fold<List<SettingLabel>>([], (previousValue, element) {
      previousValue.addAll(element);
      return previousValue;
    });
    final entries = mergedList.map((e) => MapEntry(e.uuid, e));
    return Map.fromEntries(entries).values;
  }

  Future<Iterable<SettingLabel>> _bluetoothScen() {
    final completer = Completer<Iterable<SettingLabel>>();
    // _printerManager.isScanningStream.listen((event) {
    //   print('[PrinterRepository] isScanningStream($event)');
    // });
    final map = <num, SettingLabel>{};
    _printerManager.scanResults.debounce(Duration(seconds: 1)).listen(
      (devices) {
        print('[PrinterRepository] scanResults(${devices.length})');
        // 特殊: 3 代表印表機
        final entries = devices.where((element) => 3 == element.type).map(
          (e) {
            final item = e.asSettingLabel();
            return MapEntry(item.uuid, item);
          },
        );
        map.addEntries(entries);
      },
    );
    Timer(Duration(seconds: _timeout + 2), () {
      final it = map.values;
      print('[PrinterRepository] _bluetoothScen(${it.length})');
      completer.complete(it);
    });
    _printerManager.startScan(Duration(seconds: _timeout));
    // _printerManager.stopScan();
    return completer.future;
  }
}
