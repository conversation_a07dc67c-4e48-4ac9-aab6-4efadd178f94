name: okshop_esc_pos
description: A new flutter plugin project.
version: 0.0.1
author:
homepage: https://okshop.tw
publish_to: none

environment:
  sdk: ">=2.12.0 <3.0.0"
  flutter: ">=1.20.0"

dependencies:
  flutter:
    sdk: flutter
  wifi: ^0.1.5
  ping_discover_network: ^0.2.0+1
  image: ^3.0.2
  stream_transform: ^2.0.0
  okshop_common:
    git:
        url: https://bitbucket.org/umomos/okshop_common.git
        ref: master
  okshop_model:
    git:
        url: https://bitbucket.org/umomos/okshop_model.git
        ref: master
  esc_pos_utils:
    git:
        url: https://bitbucket.org/umomos/esc_pos_utils.git
        ref: master
  esc_pos_printer:
    git:
        url: https://bitbucket.org/umomos/esc_pos_printer.git
        ref: master
  esc_pos_bluetooth:
    # path: ../esc_pos_bluetooth
    git:
        url: https://bitbucket.org/umomos/esc_pos_bluetooth.git
        ref: master
  flutter_bluetooth_basic:
    # path: ../flutter_bluetooth_basic
    git:
      url: https://bitbucket.org/umomos/flutter_bluetooth_basic.git
      ref: master

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' and Android 'package' identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: tw.okshop.okshop_esc_pos
        pluginClass: OkshopEscPosPlugin
      ios:
        pluginClass: OkshopEscPosPlugin

  # To add assets to your plugin package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
